import { components } from "./_generated/api";
import { DataModel } from "./_generated/dataModel";
import { TableAggregate } from "@convex-dev/aggregate";

// Product aggregates for efficient statistics
export const productStatsAggregate = new TableAggregate<{
  Key: string; // status
  DataModel: DataModel;
  TableName: "products";
}>(components.productStats, {
  sortKey: (doc) => doc.status,
});

// Product stock aggregate for low stock alerts
export const productStockAggregate = new TableAggregate<{
  Key: number; // stock count
  DataModel: DataModel;
  TableName: "products";
}>(components.productStock, {
  sortKey: (doc) => doc.stockCount,
});

// Order aggregates for efficient statistics
export const orderStatsAggregate = new TableAggregate<{
  Key: string; // status
  DataModel: DataModel;
  TableName: "orders";
}>(components.orderStats, {
  sortKey: (doc) => doc.status,
  sumValue: (doc) => doc.totalAmount, // For revenue calculations
});

// Order count aggregate for total counts
export const orderCountAggregate = new TableAggregate<{
  Key: null; // No sorting, just counting
  DataModel: DataModel;
  TableName: "orders";
}>(components.orderCount, {
  sortKey: () => null,
});

// Supplier aggregates
export const supplierStatsAggregate = new TableAggregate<{
  Key: boolean; // isActive
  DataModel: DataModel;
  TableName: "suppliers";
}>(components.supplierStats, {
  sortKey: (doc) => doc.isActive,
});
// Order monthly sales aggregate for efficient sales trend calculations
export const orderMonthlySalesAggregate = new TableAggregate<{
  Key: string; // YYYY-MM format (e.g., "2024-01")
  DataModel: DataModel;
  TableName: "orders";
}>(components.orderMonthlySales, {
  sortKey: (doc) => {
    const date = new Date(doc._creationTime);
    return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
  },
  sumValue: (doc) => doc.totalAmount, // Sum sales amount per month
});

// Group buy aggregates for efficient statistics
export const groupBuyStatsAggregate = new TableAggregate<{
  Key: string; // status
  DataModel: DataModel;
  TableName: "groupBuys";
}>(components.groupBuyStats, {
  sortKey: (doc) => doc.status,
  sumValue: (doc) => doc.currentParticipants, // Sum participants per status
});

// User count aggregate for efficient user statistics
export const userCountAggregate = new TableAggregate<{
  Key: null; // No sorting, just counting
  DataModel: DataModel;
  TableName: "users";
}>(components.userCount, {
  sortKey: () => null,
});

// Low stock aggregate is already available via productStockAggregate
