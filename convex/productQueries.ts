import { v } from "convex/values";
import { internalQuery } from "./_generated/server";

/**
 * Simple internal query to fetch products by IDs without complex type dependencies
 * This avoids circular type inference issues
 */
export const getProductsByIds = internalQuery({
  args: { ids: v.array(v.id("products")) },
  handler: async (ctx, args) => {
    const results = [];
    for (const id of args.ids) {
      const doc = await ctx.db.get(id);
      if (doc !== null) {
        results.push(doc);
      }
    }
    return results;
  },
});
