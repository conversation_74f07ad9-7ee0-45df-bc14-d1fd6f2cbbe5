import { v } from "convex/values";
import { mutation, query, QueryCtx } from "./_generated/server";
import { Doc, Id } from "./_generated/dataModel";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";
import { supplierStatsAggregate } from "./aggregates";
import { getAuthUserId } from "@convex-dev/auth/server";



// Query to get all suppliers with pagination
export const getSuppliers = query({
  args: {
    activeOnly: v.optional(v.boolean()),
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
  },
  handler: async (ctx, { activeOnly = false, paginationOpts }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_VIEW);

    // Build query with proper initialization
    let query;

    if (activeOnly) {
      query = ctx.db.query("suppliers").withIndex("by_active", (q) => q.eq("isActive", true));
    } else {
      query = ctx.db.query("suppliers");
    }

    // PERFORMANCE: Always use pagination to prevent unbounded queries
    const defaultPagination = { numItems: 50, cursor: null };
    const results = await query.paginate(paginationOpts || defaultPagination);
    const suppliers = results.page;

    // PERFORMANCE OPTIMIZATION: Batch fetch product counts instead of N+1 queries
    const supplierIds = suppliers.map(s => s._id);
    const productCountsMap = await batchFetchSupplierProductCounts(ctx, supplierIds);

    const suppliersWithStats = suppliers.map(supplier => ({
      ...supplier,
      productCount: productCountsMap.get(supplier._id) || { active: 0, total: 0 },
    }));

    // Return paginated results with enriched data
    return {
      ...results,
      page: suppliersWithStats,
    };
  },
});

// Query to get a single supplier by ID
export const getSupplier = query({
  args: { id: v.id("suppliers") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_VIEW);

    const supplier = await ctx.db.get(id);
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Get products for this supplier
    const products = await ctx.db
      .query("products")
      .withIndex("by_supplier", (q) => q.eq("supplierId", id))
      .collect();

    const createdBy = await ctx.db.get(supplier.createdBy);

    return {
      ...supplier,
      products: products.map(p => ({
        id: p._id,
        title: p.title,
        status: p.status,
        stockCount: p.stockCount,
        finalPrice: p.finalPrice,
      })),
      createdByUser: createdBy ? { name: createdBy.name, email: createdBy.email } : null,
    };
  },
});

// Mutation to create a new supplier
export const createSupplier = mutation({
  args: {
    name: v.string(),
    contactInfo: v.object({
      email: v.optional(v.string()),
      phone: v.optional(v.string()),
      address: v.optional(v.string()),
    }),
    platformUrl: v.optional(v.string()),
    rating: v.optional(v.number()),
    notes: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_CREATE);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Validate input
    if (!args.name.trim()) {
      throw new Error("Supplier name is required");
    }

    // Check if supplier with same name already exists
    const existingSupplier = await ctx.db
      .query("suppliers")
      .filter((q) => q.eq(q.field("name"), args.name))
      .first();

    if (existingSupplier) {
      throw new Error("Supplier with this name already exists");
    }

    // Validate email if provided
    if (args.contactInfo.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(args.contactInfo.email)) {
        throw new Error("Invalid email format");
      }
    }

    // Validate rating if provided
    if (args.rating !== undefined && (args.rating < 0 || args.rating > 5)) {
      throw new Error("Rating must be between 0 and 5");
    }

    const supplierId = await ctx.db.insert("suppliers", {
      ...args,
      isActive: args.isActive ?? true,
      createdBy: userId,
    });

    // Update aggregates
    const supplier = await ctx.db.get(supplierId);
    if (supplier) {
      await supplierStatsAggregate.insert(ctx, supplier);
    }

    return supplierId;
  },
});

// Mutation to update a supplier
export const updateSupplier = mutation({
  args: {
    id: v.id("suppliers"),
    name: v.optional(v.string()),
    contactInfo: v.optional(v.object({
      email: v.optional(v.string()),
      phone: v.optional(v.string()),
      address: v.optional(v.string()),
    })),
    platformUrl: v.optional(v.string()),
    rating: v.optional(v.number()),
    notes: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, { id, ...updates }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_EDIT);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const supplier = await ctx.db.get(id);
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Validate name if being updated
    if (updates.name) {
      if (!updates.name.trim()) {
        throw new Error("Supplier name is required");
      }

      // Check if another supplier with same name exists
      const existingSupplier = await ctx.db
        .query("suppliers")
        .filter((q) => 
          q.and(
            q.eq(q.field("name"), updates.name),
            q.neq(q.field("_id"), id)
          )
        )
        .first();

      if (existingSupplier) {
        throw new Error("Supplier with this name already exists");
      }
    }

    // Validate email if being updated
    if (updates.contactInfo?.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(updates.contactInfo.email)) {
        throw new Error("Invalid email format");
      }
    }

    // Validate rating if being updated
    if (updates.rating !== undefined && (updates.rating < 0 || updates.rating > 5)) {
      throw new Error("Rating must be between 0 and 5");
    }

    await ctx.db.patch(id, updates);

    // Update aggregates
    const updatedSupplier = await ctx.db.get(id);
    if (updatedSupplier) {
      await supplierStatsAggregate.replace(ctx, supplier, updatedSupplier);
    }

    return { success: true };
  },
});

// Mutation to delete a supplier (soft delete by setting isActive to false)
export const deleteSupplier = mutation({
  args: { id: v.id("suppliers") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_DELETE);

    const supplier = await ctx.db.get(id);
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Check if supplier has active products
    const activeProducts = await ctx.db
      .query("products")
      .withIndex("by_supplier", (q) => q.eq("supplierId", id))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    if (activeProducts.length > 0) {
      throw new Error("Cannot delete supplier with active products. Please archive the products first.");
    }

    await ctx.db.patch(id, {
      isActive: false,
    });

    // Update aggregates
    const updatedSupplier = await ctx.db.get(id);
    if (updatedSupplier) {
      await supplierStatsAggregate.replace(ctx, supplier, updatedSupplier);
    }

    return { success: true };
  },
});

// Query to search suppliers by name - PERFORMANCE OPTIMIZED
export const searchSuppliers = query({
  args: {
    searchTerm: v.string(),
    limit: v.optional(v.number()),
    activeOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, { searchTerm, limit = 20, activeOnly = false }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_VIEW);

    if (!searchTerm.trim()) {
      return [];
    }

    // PERFORMANCE OPTIMIZATION: Use database filtering instead of full table scan
    let query;

    // Apply active filter at database level if specified
    if (activeOnly) {
      query = ctx.db.query("suppliers").withIndex("by_active", (q) => q.eq("isActive", true));
    } else {
      query = ctx.db.query("suppliers");
    }

    // Get suppliers and filter efficiently
    const suppliers = await query.collect();

    const searchTermLower = searchTerm.toLowerCase();
    const searchResults = suppliers
      .filter(supplier =>
        supplier.name.toLowerCase().includes(searchTermLower) ||
        (supplier.notes && supplier.notes.toLowerCase().includes(searchTermLower))
      )
      .slice(0, limit);

    // PERFORMANCE OPTIMIZATION: Batch fetch product counts
    const supplierIds = searchResults.map(s => s._id);
    const productCountsMap = await batchFetchSupplierProductCounts(ctx, supplierIds);

    return searchResults.map(supplier => ({
      ...supplier,
      productCount: productCountsMap.get(supplier._id) || { active: 0, total: 0 },
    }));
  },
});

// Helper function to batch fetch product counts for suppliers
async function batchFetchSupplierProductCounts(ctx: QueryCtx, supplierIds: Id<"suppliers">[]) {
  const productCountsMap = new Map();

  // Batch fetch all products for these suppliers
  const allProducts = await Promise.all(
    supplierIds.map(supplierId =>
      ctx.db
        .query("products")
        .withIndex("by_supplier", (q) => q.eq("supplierId", supplierId))
        .collect()
    )
  );

  // Calculate counts for each supplier
  supplierIds.forEach((supplierId, index) => {
    const products = allProducts[index];
    const activeProducts = products.filter((p: Doc<"products">) => p.status === "active").length;
    productCountsMap.set(supplierId, {
      active: activeProducts,
      total: products.length,
    });
  });

  return productCountsMap;
}

// Query to get supplier statistics using efficient aggregates
export const getSupplierStats = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    // Use aggregates for O(log n) performance instead of O(n)
    const total = await supplierStatsAggregate.count(ctx);
    const active = await supplierStatsAggregate.count(ctx, {
      bounds: { lower: { key: true, inclusive: true }, upper: { key: true, inclusive: true } }
    });
    const inactive = await supplierStatsAggregate.count(ctx, {
      bounds: { lower: { key: false, inclusive: true }, upper: { key: false, inclusive: true } }
    });

    // PERFORMANCE OPTIMIZATION: More efficient way to count suppliers with/without products
    // Get all suppliers and all products in parallel
    const [suppliers, allProducts] = await Promise.all([
      ctx.db.query("suppliers").collect(),
      ctx.db.query("products").collect()
    ]);

    // Create a set of supplier IDs that have products
    const supplierIdsWithProducts = new Set(allProducts.map(p => p.supplierId));

    const withProducts = suppliers.filter(s => supplierIdsWithProducts.has(s._id)).length;
    const withoutProducts = suppliers.length - withProducts;

    const stats = {
      total,
      active,
      inactive,
      withProducts,
      withoutProducts,
    };

    return stats;
  },
});
