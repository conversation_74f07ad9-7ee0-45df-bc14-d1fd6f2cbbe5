import { v } from "convex/values";
import { internalMutation, query } from "./_generated/server";

// Query to get all unique tags
export const get = query({
  handler: async (ctx) => {
    const tags = await ctx.db.query("uniqueTags").collect();
    return tags.map((t) => t.tag);
  },
});

// Internal mutation to add new tags to the collection
export const addTags = internalMutation({
  args: {
    tags: v.array(v.string()),
  },
  handler: async (ctx, { tags }) => {
    if (tags.length === 0) {
      return;
    }

    // PERFORMANCE OPTIMIZATION: Use pagination to avoid unbounded queries
    // For large tag collections, use index-based lookups instead of collect()
    const existingTagsResult = await ctx.db.query("uniqueTags")
      .paginate({ numItems: 1000, cursor: null });

    const existingTags = new Set(existingTagsResult.page.map((t) => t.tag));

    // If we have more tags, continue paginating (for very large tag collections)
    let cursor = existingTagsResult.continueCursor;
    while (cursor) {
      const nextPage = await ctx.db.query("uniqueTags")
        .paginate({ numItems: 1000, cursor });

      nextPage.page.forEach(t => existingTags.add(t.tag));
      cursor = nextPage.continueCursor;

      // Safety break to prevent infinite loops
      if (existingTags.size > 10000) {
        console.warn("Tag collection is very large, consider using index-based lookups");
        break;
      }
    }

    const newTags = tags.filter((tag) => !existingTags.has(tag));

    // PERFORMANCE: Batch insert new tags to reduce database calls
    const insertPromises = newTags.map(async (tag) => {
      try {
        await ctx.db.insert("uniqueTags", { tag });
      } catch (error) {
        // If the tag already exists due to a race condition, we can ignore the error
        console.warn(`Could not insert tag, it might already exist: ${tag}`, error);
      }
    });

    // Execute all inserts in parallel
    await Promise.all(insertPromises);
  },
});
