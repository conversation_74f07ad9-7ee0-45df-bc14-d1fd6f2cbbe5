import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

export default defineSchema({
  ...authTables,

  // PERFORMANCE: Add email index to users table (from authTables)
  // Note: This extends the auth tables with additional indexes
  users: defineTable({
    name: v.optional(v.string()),
    email: v.optional(v.string()),
    emailVerificationTime: v.optional(v.number()),
    phone: v.optional(v.string()),
    phoneVerificationTime: v.optional(v.number()),
    isAnonymous: v.optional(v.boolean()),
    image: v.optional(v.string()),
  }).index("by_email", ["email"]), // PERFORMANCE: Critical index for email lookups

  adminUsers: defineTable({
    userId: v.id("users"),
    role: v.union(v.literal("super_admin"), v.literal("admin"), v.literal("moderator")),
    permissions: v.array(v.string()),
    createdBy: v.id("users"),
    isActive: v.boolean(),
    lastLoginAt: v.optional(v.number()),
  }).index("by_user", ["userId"])
    .index("by_role", ["role"])
    .index("by_active", ["isActive"]),

  products: defineTable({
    title: v.string(),
    description: v.string(),
    curationNotes: v.string(),
    supplierId: v.id("suppliers"),
    priceInYuan: v.number(),
    serviceFee: v.number(),
    finalPrice: v.number(),
    tags: v.array(v.string()),
    images: v.array(v.union(v.string(), v.id("_storage"))),
    stockCount: v.number(),
    status: v.union(v.literal("active"), v.literal("inactive"), v.literal("archived")),
    createdBy: v.id("users"),
    updatedBy: v.id("users"),
    providerData: v.object({
      source: v.string(),
      productUrl: v.string(),
      providerId: v.string(),
      lastScraped: v.number(),
      providerSpecificData: v.optional(v.any()),
    }),
    pricingTiers: v.array(v.object({
      minQuantity: v.number(),
      maxQuantity: v.optional(v.number()),
      price: v.number(),
      currency: v.string(),
      // Legacy fields for backward compatibility
      discountPercentage: v.optional(v.number()),
      regionCode: v.optional(v.string()),
      participantRequirement: v.optional(v.number()),
      timeConstraint: v.optional(v.object({
        startTime: v.number(),
        endTime: v.number()
      })),
    })),
    variants: v.array(v.object({
      type: v.string(),
      name: v.string(),
      value: v.string(),
      priceModifier: v.optional(v.number()),
      // Legacy fields for backward compatibility
      priceType: v.optional(v.string()),
      absolutePrice: v.optional(v.number()),
      currency: v.optional(v.string()),
      availableQuantity: v.optional(v.number()),
      images: v.optional(v.array(v.string())),
    })),
    customServices: v.array(v.object({
      name: v.string(),
      price: v.number(),
      isRequired: v.boolean(),
      // Legacy fields for backward compatibility
      description: v.optional(v.string()),
      minQuantity: v.optional(v.number()),
      currency: v.optional(v.string()),
    })),
    attributes: v.array(v.object({ 
      name: v.string(), 
      value: v.any()
    })),

    // Denormalized supplier data for performance
    supplier: v.optional(v.object({
        id: v.id("suppliers"),
        name: v.string(),
    })),
    // Embedding field for vector search (nullable for products without embeddings)
    imageEmbedding: v.optional(v.union(v.array(v.float64()), v.null())),
  })
    // --- INDEXES FOR PERFORMANCE ---
    // Filter indexes
    .index("by_supplier", ["supplierId"])
    .index("by_status", ["status"])
    .index("by_stockCount", ["stockCount"])
    .index("by_finalPrice", ["finalPrice"])
    .index("by_created_by", ["createdBy"])
    .index("by_updated_by", ["updatedBy"]) // PERFORMANCE: For tracking user updates
    .index("by_provider_product_url", ["providerData.productUrl"])
    // PERFORMANCE: Compound indexes for common query patterns
    .index("by_supplier_status", ["supplierId", "status"])
    .index("by_status_stock", ["status", "stockCount"])
    // Search index for advanced text search
    .searchIndex("search_all", {
      searchField: "title",
      filterFields: ["description", "tags"],
    })
    // Vector index for image similarity search
    .vectorIndex("by_embedding", {
      vectorField: "imageEmbedding",
      dimensions: 1536, // NOTE: This must match your embedding model's dimensions
      filterFields: ["status", "supplierId"],
    }),

  suppliers: defineTable({
    name: v.string(),
    contactInfo: v.object({
      email: v.optional(v.string()),
      phone: v.optional(v.string()),
      address: v.optional(v.string()),
    }),
    platformUrl: v.optional(v.string()),
    rating: v.optional(v.number()),
    notes: v.optional(v.string()),
    isActive: v.boolean(),
    createdBy: v.id("users"),
  }).index("by_active", ["isActive"])
    .index("by_created_by", ["createdBy"]),

  orders: defineTable({
    userId: v.id("users"),
    items: v.array(v.object({
      productId: v.id("products"),
      quantity: v.number(),
      priceAtTime: v.number(),
      title: v.string(),
    })),
    status: v.union(
      v.literal("new"),
      v.literal("sourcing"),
      v.literal("action_required"),
      v.literal("shipped"),
      v.literal("delivered"),
      v.literal("cancelled")
    ),
    trackingNumber: v.optional(v.string()),
    shippingAddress: v.object({
      name: v.string(),
      address: v.string(),
      city: v.string(),
      country: v.string(),
      postalCode: v.string(),
    }),
    communicationHistory: v.array(v.object({
      message: v.string(),
      fromAdmin: v.boolean(),
      adminUserId: v.optional(v.id("users")),
      timestamp: v.number(),
    })),
    issueResolution: v.optional(v.object({
      issue: v.string(),
      suggestedAlternatives: v.array(v.id("products")),
      resolution: v.optional(v.string()),
    })),
    totalAmount: v.number(),
    assignedTo: v.optional(v.id("users")),
    providerOrderData: v.object({
      selectedVariant: v.optional(v.object({
        type: v.string(),
        name: v.string(),
        value: v.string(),
        priceType: v.string(),
        finalPrice: v.number(),
      })),
      selectedQuantityTier: v.object({
        minQuantity: v.number(),
        price: v.number(),
        currency: v.string(),
      }),
      selectedCustomServices: v.array(v.object({
        name: v.string(),
        price: v.number(),
      })),
      totalProviderCost: v.number(),
      pricingBreakdown: v.object({
        basePrice: v.number(),
        variantAdjustment: v.number(),
        quantityDiscount: v.number(),
        customServicesTotal: v.number(),
        ourCommission: v.number(),
      }),
    }),
  }).index("by_user", ["userId"])
    .index("by_status", ["status"])
    .index("by_assigned_to", ["assignedTo"]),

  groupBuys: defineTable({
    productId: v.id("products"),
    targetTiers: v.array(v.object({
      quantity: v.number(),
      price: v.number(),
    })),
    currentParticipants: v.number(),
    status: v.union(v.literal("active"), v.literal("completed"), v.literal("expired")),
    startTime: v.number(),
    endTime: v.number(),
    createdBy: v.id("users"),
    providerPricingTiers: v.array(v.object({
      minParticipants: v.number(),
      pricePerUnit: v.number(),
      ourCommission: v.number(),
      finalPrice: v.number(),
    })),
  }).index("by_product", ["productId"])
    .index("by_status", ["status"])
    .index("by_end_time", ["endTime"]),

  groupBuyParticipants: defineTable({
    groupBuyId: v.id("groupBuys"),
    userId: v.id("users"),
    quantity: v.number(),
    joinedAt: v.number(),
  }).index("by_group_buy_and_user", ["groupBuyId", "userId"])
    .index("by_group_buy", ["groupBuyId"]),

  // New table for efficient tag lookups
  uniqueTags: defineTable({
    tag: v.string(),
  }).index("by_tag", ["tag"]),
});
