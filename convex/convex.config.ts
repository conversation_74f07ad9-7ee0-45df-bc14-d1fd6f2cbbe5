import { defineApp } from "convex/server";
import aggregate from "@convex-dev/aggregate/convex.config";

const app = defineApp();
app.use(aggregate, { name: "productStats" });
app.use(aggregate, { name: "productStock" });
app.use(aggregate, { name: "orderStats" });
app.use(aggregate, { name: "orderCount" });
app.use(aggregate, { name: "supplierStats" });
app.use(aggregate, { name: "orderMonthlySales" });
app.use(aggregate, { name: "groupBuyStats" });
app.use(aggregate, { name: "userCount" });
export default app;
