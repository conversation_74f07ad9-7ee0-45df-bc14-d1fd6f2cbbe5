import { v } from "convex/values";
import { mutation, query, QueryCtx } from "./_generated/server";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Doc, Id } from "./_generated/dataModel";
import { orderCountAggregate, orderStatsAggregate, userCountAggregate } from "./aggregates";

// PERFORMANCE OPTIMIZATION: Batch fetch helper functions
async function batchFetchAdminUsers(ctx: QueryCtx, userIds: Id<"users">[]) {
  // PERFORMANCE: Use pagination instead of collect() for large datasets
  const adminUsersResult = await ctx.db.query("adminUsers").paginate({ numItems: 1000, cursor: null });
  const adminUsers = adminUsersResult.page;
  const adminUsersMap = new Map<Id<"users">, Doc<"adminUsers"> | null>();

  userIds.forEach(userId => {
    const adminUser = adminUsers.find((admin: Doc<"adminUsers">) => admin.userId === userId);
    adminUsersMap.set(userId, adminUser || null);
  });

  return adminUsersMap;
}

async function batchFetchOrderCounts(ctx: QueryCtx, userIds: Id<"users">[]) {
  // PERFORMANCE: Use pagination instead of collect() for large datasets
  const allOrdersResult = await ctx.db.query("orders").paginate({ numItems: 2000, cursor: null });
  const allOrders = allOrdersResult.page;
  const orderCountsMap = new Map<Id<"users">, number>();

  userIds.forEach(userId => {
    const userOrders = allOrders.filter((order: Doc<"orders">) => order.userId === userId);
    orderCountsMap.set(userId, userOrders.length);
  });

  return orderCountsMap;
}

async function enrichUsersWithBatchFetch(ctx: QueryCtx, users: Doc<"users">[]) {
  if (users.length === 0) return [];

  const userIds = users.map(user => user._id);

  // Batch fetch admin status and order counts in parallel
  const [adminUsersMap, orderCountsMap] = await Promise.all([
    batchFetchAdminUsers(ctx, userIds),
    batchFetchOrderCounts(ctx, userIds)
  ]);

  return users.map(user => {
    const adminUser = adminUsersMap.get(user._id);
    const ordersCount = orderCountsMap.get(user._id) || 0;

    return {
      ...user,
      isAdmin: !!adminUser,
      adminRole: adminUser?.role || null,
      isAdminActive: adminUser?.isActive || false,
      ordersCount,
      lastLoginAt: adminUser?.lastLoginAt || null,
    };
  });
}

// Query to get all users with pagination and filtering
export const getUsers = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.union(v.string(), v.null()),
    })),
    search: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, { paginationOpts, search }) => {
    await requirePermission(ctx, PERMISSIONS.USERS_VIEW);

    // Build query
    const query = ctx.db.query("users");

    // Apply pagination if provided
    if (paginationOpts) {
      const results = await query.paginate(paginationOpts);
      
      // Filter and enrich users
      let filteredUsers = results.page;
      
      if (search) {
        const searchLower = search.toLowerCase();
        filteredUsers = filteredUsers.filter(user => 
          user.name?.toLowerCase().includes(searchLower) ||
          user.email?.toLowerCase().includes(searchLower)
        );
      }

      // PERFORMANCE OPTIMIZATION: Use batch fetch
      const enrichedUsers = await enrichUsersWithBatchFetch(ctx, filteredUsers);

      return {
        ...results,
        page: enrichedUsers,
      };
    }

    // PERFORMANCE OPTIMIZATION: Always use pagination to prevent unbounded queries
    const defaultPagination = { numItems: 50, cursor: null };
    const results = await query.paginate(defaultPagination);

    // Apply filters
    let filteredUsers = results.page;

    if (search) {
      const searchLower = search.toLowerCase();
      filteredUsers = filteredUsers.filter(user =>
        user.name?.toLowerCase().includes(searchLower) ||
        user.email?.toLowerCase().includes(searchLower)
      );
    }

    // PERFORMANCE OPTIMIZATION: Use batch fetch
    const enrichedUsers = await enrichUsersWithBatchFetch(ctx, filteredUsers);

    return {
      ...results,
      page: enrichedUsers,
      totalFiltered: filteredUsers.length,
      hasSearch: !!search?.trim(),
    };
  },
});

// Query to get a single user by ID
export const getUser = query({
  args: { id: v.id("users") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.USERS_VIEW);

    const user = await ctx.db.get(id);
    if (!user) {
      throw new Error("User not found");
    }

    // Get admin status
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", id))
      .first();

    // Get user's orders
    const orders = await ctx.db
      .query("orders")
      .withIndex("by_user", (q) => q.eq("userId", id))
      .collect();

    // Get user's created products
    const createdProducts = await ctx.db
      .query("products")
      .withIndex("by_created_by", (q) => q.eq("createdBy", id))
      .collect();

    return {
      ...user,
      isAdmin: !!adminUser,
      adminRole: adminUser?.role || null,
      isAdminActive: adminUser?.isActive || false,
      lastLoginAt: adminUser?.lastLoginAt || null,
      orders: orders.map(order => ({
        id: order._id,
        status: order.status,
        totalAmount: order.totalAmount,
        createdAt: order._creationTime,
      })),
      createdProducts: createdProducts.map(product => ({
        id: product._id,
        title: product.title,
        status: product.status,
        finalPrice: product.finalPrice,
      })),
      stats: {
        totalOrders: orders.length,
        totalSpent: orders.reduce((sum, order) => sum + order.totalAmount, 0),
        productsCreated: createdProducts.length,
      },
    };
  },
});

// Query to check if a user is an active admin
export const isUserAdmin = query({
  args: { id: v.id("users") },
  handler: async (ctx, { id }) => {
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", id))
      .first();
    return !!(adminUser && adminUser.isActive);
  },
});

// Query to get user statistics - PERFORMANCE OPTIMIZED
export const getUserStats = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    // PERFORMANCE OPTIMIZATION: Use aggregates for O(log n) performance
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);

    // PERFORMANCE: Use aggregates and efficient counting with proper limits
    const [
      totalUsers,
      totalAdmins,
      activeAdmins,
      totalOrders,
      recentUsers,
      recentOrders,
      deliveredOrders
    ] = await Promise.all([
      // PERFORMANCE: Use user count aggregate for O(log n) performance
      userCountAggregate.count(ctx),

      // Count admin users efficiently
      ctx.db.query("adminUsers")
        .paginate({ numItems: 50, cursor: null })
        .then(result => result.page.length),

      // Count active admins with proper index usage
      ctx.db.query("adminUsers")
        .withIndex("by_active", q => q.eq("isActive", true))
        .paginate({ numItems: 50, cursor: null })
        .then(result => result.page.length),

      // PERFORMANCE: Use order aggregates for accurate counting
      orderCountAggregate.count(ctx),

      // Count recent users with efficient date filtering
      ctx.db.query("users")
        .filter(q => q.gt(q.field("_creationTime"), thirtyDaysAgo))
        .paginate({ numItems: 200, cursor: null })
        .then(result => result.page.length),

      // Get recent orders for active user calculation (optimized limit)
      ctx.db.query("orders")
        .filter(q => q.gt(q.field("_creationTime"), thirtyDaysAgo))
        .paginate({ numItems: 500, cursor: null })
        .then(result => result.page),

      // Use order aggregate for delivered orders revenue
      orderStatsAggregate.sum(ctx, {
        bounds: { lower: { key: "delivered", inclusive: true }, upper: { key: "delivered", inclusive: true } }
      }).then(async (totalRevenue) => {
        // Also get count for average calculation
        const deliveredCount = await orderStatsAggregate.count(ctx, {
          bounds: { lower: { key: "delivered", inclusive: true }, upper: { key: "delivered", inclusive: true } }
        });
        return { totalRevenue, count: deliveredCount };
      })
    ]);

    const totalRevenue = deliveredOrders.totalRevenue || 0;
    const avgOrderValue = deliveredOrders.count > 0 ? totalRevenue / deliveredOrders.count : 0;
    const activeUsers = new Set(recentOrders.map(order => order.userId)).size;

    return {
      totalUsers,
      totalAdmins,
      activeAdmins,
      activeUsers,
      totalOrders,
      totalRevenue,
      avgOrderValue,
      userGrowth: {
        thisMonth: recentUsers,
      },
    };
  },
});

// Mutation to update user profile (limited fields)
export const updateUserProfile = mutation({
  args: {
    id: v.id("users"),
    name: v.optional(v.string()),
    email: v.optional(v.string()),
  },
  handler: async (ctx, { id, ...updates }) => {
    await requirePermission(ctx, PERMISSIONS.USERS_EDIT);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db.get(id);
    if (!user) {
      throw new Error("User not found");
    }

    // Validate email if being updated
    if (updates.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(updates.email)) {
        throw new Error("Invalid email format");
      }

      // Check if email is already taken
      const existingUser = await ctx.db
        .query("users")
        .filter((q) => 
          q.and(
            q.eq(q.field("email"), updates.email),
            q.neq(q.field("_id"), id)
          )
        )
        .first();

      if (existingUser) {
        throw new Error("Email is already taken");
      }
    }

    // Validate name
    if (updates.name && !updates.name.trim()) {
      throw new Error("Name cannot be empty");
    }

    await ctx.db.patch(id, updates);

    return { success: true };
  },
});

// Mutation to deactivate a user (soft delete)
export const deactivateUser = mutation({
  args: { id: v.id("users") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.USERS_DELETE);

    const userId = await getAuthUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const user = await ctx.db.get(id);
    if (!user) {
      throw new Error("User not found");
    }

    // Check if user is an admin
    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", id))
      .first();

    if (adminUser) {
      // Deactivate admin privileges first
      await ctx.db.patch(adminUser._id, { isActive: false });
    }

    // For now, we'll just mark them as inactive in admin table
    // In a real app, you might want to add an isActive field to users table
    return { success: true };
  },
});
